import { vector } from '@flatten-js/core';
import { buildDocumentAwarenessCmdOption } from '@viclass/editor.core';
import { syncRenderCommands } from '../cmd';
import { CommonToolState, GeoElConstructionRequest, RenderLine, RenderVertex } from '../model';
import { GeoPointerEvent } from '../model/geo.models';
import { pLine, PreviewQueue } from '../model/preview.util';
import { GeoDocCtrl, GeoSelectHitContext } from '../objects';
import { or, stroke, then, ThenSelector, vertex, vertexOnStroke, vert } from '../selectors';
import { GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import {
    addHistoryItemFromConstructionResponse,
    buildPreviewVertexRenderProp,
    calculateScalingFactor,
    calculateUnitVector,
    getFocusDocCtrl,
    handleIfPointerNotInError,
    buildPointConstruction,
    isElementLine,
    isSamePoint,
    pickPointName,
    projectPointOntoLine,
    requestElementNames,
} from './tool.utils';

/**
 * Creates the common selection logic for parallel/perpendicular line tools
 * @param pQ - Preview queue for the tool
 * @param pointerHandler - Pointer handler with cursor
 * @param onComplete - Callback when selection is complete
 * @returns ThenSelector with line -> point -> final vertex selection logic
 */
export function createLineToolSelLogic(
    pQ: PreviewQueue,
    pointerHandler: { cursor: any },
    onComplete: (selector: ThenSelector, doc: GeoDocCtrl) => Promise<void>
): ThenSelector {
    // First select a line
    const lineSelector = stroke({
        selectableStrokeTypes: ['RenderLine', 'RenderLineSegment', 'RenderRay', 'RenderVector'],
        previewQueue: pQ,
        cursor: pointerHandler.cursor,
    });

    // Then select a point
    const pointSelector = vertex({
        previewQueue: pQ,
        cursor: pointerHandler.cursor,
    });

    // Finally select a vertex or stroke for construction
    const finalSelector = or(
        [
            vertex({
                previewQueue: pQ,
                cursor: pointerHandler.cursor,
            }),
            stroke({
                selectableStrokeTypes: ['RenderLine', 'RenderLineSegment', 'RenderRay', 'RenderVector'],
                previewQueue: pQ,
                cursor: pointerHandler.cursor,
            }),
            vertexOnStroke({
                selectableStrokeTypes: ['RenderLine', 'RenderLineSegment', 'RenderRay', 'RenderVector'],
                previewQueue: pQ,
                cursor: pointerHandler.cursor,
                preview: true, // Enable preview element selection
                syncPreview: true,
            }),
        ],
        { flatten: true }
    );

    // Main selection logic: select line, then point, then show preview and select final vertex or stroke
    return then([lineSelector, pointSelector, finalSelector], {
        onComplete: onComplete,
    });
}

/**
 * Shows preview line immediately after selecting a line, before selecting any point
 * @param selectedLine - The selected line
 * @param ctrl - Document controller
 * @param pQ - Preview queue
 * @param previewId - Preview ID to use
 * @param isPerpendicularVector - Whether to show perpendicular (true) or parallel (false) line
 * @returns The created preview line
 */
function showPreviewLineAfterLineSelection(
    selectedLine: RenderLine,
    ctrl: GeoDocCtrl,
    pQ: PreviewQueue,
    previewId: number,
    isPerpendicularVector: boolean = false
): RenderLine | undefined {
    if (!selectedLine) return undefined;

    // Calculate line vector (perpendicular or parallel)
    const lineVector = selectedLine.vector;
    if (!lineVector || lineVector.length < 2) return undefined;

    const calculatedVector = isPerpendicularVector ? [-lineVector[1], lineVector[0], lineVector[2] || 0] : lineVector;

    // Get start and end coordinates of the selected line
    const startCoords = selectedLine.coord('start', ctrl.rendererCtrl);
    const endCoords = selectedLine.coord('end', ctrl.rendererCtrl);

    if (!startCoords || !endCoords || startCoords.length < 2 || endCoords.length < 2) return undefined;

    // Create a preview line at the center of the selected line as a starting point
    const lineMidpoint = {
        coords: [
            (startCoords[0] + endCoords[0]) / 2,
            (startCoords[1] + endCoords[1]) / 2,
            (startCoords[2] || 0 + endCoords[2] || 0) / 2, // Z coordinate
        ],
        name: 'temp_midpoint',
        relIndex: -33, // Unique preview ID for midpoint
        usable: true,
        valid: true,
    } as RenderVertex;

    const previewLine = pLine(ctrl, previewId, RenderLine, lineMidpoint, undefined, calculatedVector);

    if (previewLine) {
        pQ.add(previewLine);
    }

    return previewLine;
}

/**
 * Shows preview line after selecting line and point
 * @param selectedLine - The selected line
 * @param selectedPoint - The selected point
 * @param ctrl - Document controller
 * @param pQ - Preview queue
 * @param previewId - Preview ID to use
 * @param isPerpendicularVector - Whether to show perpendicular (true) or parallel (false) line
 * @returns The created preview line
 */
function showPreviewLine(
    selectedLine: RenderLine,
    selectedPoint: RenderVertex,
    ctrl: GeoDocCtrl,
    pQ: PreviewQueue,
    previewId: number,
    isPerpendicularVector: boolean = false
): RenderLine | undefined {
    if (!selectedLine || !selectedPoint) return undefined;

    // Calculate line vector (perpendicular or parallel)
    const lineVector = selectedLine.vector;
    if (!lineVector || lineVector.length < 2) return undefined;

    const calculatedVector = isPerpendicularVector ? [-lineVector[1], lineVector[0], lineVector[2] || 0] : lineVector;

    // Create preview line through the selected point
    const previewLine = pLine(ctrl, previewId, RenderLine, selectedPoint, undefined, calculatedVector);

    pQ.add(previewLine);
    return previewLine;
}

/**
 * Updates the preview line to go through a different vertex
 * @param selectedLine - The selected line
 * @param trialVertex - The vertex to update preview to
 * @param ctrl - Document controller
 * @param pQ - Preview queue
 * @param previewId - Preview ID to use
 * @param isPerpendicularVector - Whether to show perpendicular (true) or parallel (false) line
 * @returns The updated preview line
 */
function updatePreviewLineToVertex(
    selectedLine: RenderLine,
    trialVertex: any,
    ctrl: GeoDocCtrl,
    pQ: PreviewQueue,
    previewId: number,
    isPerpendicularVector: boolean = false
): RenderLine | undefined {
    if (!selectedLine || !trialVertex) return undefined;

    // Extract the actual RenderVertex from the trial selection
    const vertex = vert(trialVertex);
    if (!vertex || !vertex.coords || vertex.coords.length < 2) return undefined;

    // Calculate line vector (perpendicular or parallel)
    const lineVector = selectedLine.vector;
    if (!lineVector || lineVector.length < 2) return undefined;

    const calculatedVector = isPerpendicularVector ? [-lineVector[1], lineVector[0], lineVector[2] || 0] : lineVector;

    // Create new preview line through the hovered vertex
    // Using the same ID will replace the existing preview line
    const previewLine = pLine(
        ctrl,
        previewId, // Same ID replaces existing
        RenderLine,
        vertex,
        undefined,
        calculatedVector
    );

    pQ.add(previewLine);
    return previewLine;
}

/**
 * Common pointer event handling logic for parallel/perpendicular line tools
 * @param geoTool - The geometry tool instance
 * @param event - The pointer event
 * @param selLogic - The selection logic
 * @param pQ - Preview queue
 * @param previewId - Preview ID to use
 * @param isPerpendicularVector - Whether this is for perpendicular (true) or parallel (false) lines
 * @param toolState - Object containing tool state (selectedLine, selectedPoint, previewLine, isShowingPreview)
 */
export function handleLineToolPointerEvent(
    geoTool: GeometryTool<CommonToolState>,
    event: GeoPointerEvent,
    selLogic: ThenSelector,
    pQ: PreviewQueue,
    previewId: number,
    isPerpendicularVector: boolean,
    toolState: {
        selectedLine?: RenderLine;
        selectedPoint?: RenderVertex;
        previewLine?: RenderLine;
        isShowingPreview: boolean;
        setSelectedLine: (line: RenderLine | undefined) => void;
        setSelectedPoint: (point: RenderVertex | undefined) => void;
        setPreviewLine: (line: RenderLine | undefined) => void;
        setIsShowingPreview: (showing: boolean) => void;
    }
): GeoPointerEvent {
    if (event.eventType == 'pointerdown') {
        if (!geoTool.shouldHandleClick(event)) return event;
    }

    const ctrl = getFocusDocCtrl(geoTool.editor, event.viewport.id);
    if (!ctrl?.state) return event;

    if (event.eventType == 'pointermove') {
        (geoTool as any).pointerMoveCachingReflowSync.handleEvent(event, (event: any) =>
            handleIfPointerNotInError(geoTool, () =>
                doTrySelection(event, ctrl, selLogic, pQ, previewId, isPerpendicularVector, toolState)
            )
        );
    } else {
        doTrySelection(event, ctrl, selLogic, pQ, previewId, isPerpendicularVector, toolState);
    }

    event.continue = false;
    event.nativeEvent.preventDefault();

    return event;
}

/**
 * Internal function to handle selection logic
 */
function doTrySelection(
    event: any,
    ctrl: GeoDocCtrl,
    selLogic: ThenSelector,
    pQ: PreviewQueue,
    previewId: number,
    isPerpendicularVector: boolean,
    toolState: {
        selectedLine?: RenderLine;
        selectedPoint?: RenderVertex;
        previewLine?: RenderLine;
        isShowingPreview: boolean;
        setSelectedLine: (line: RenderLine | undefined) => void;
        setSelectedPoint: (point: RenderVertex | undefined) => void;
        setPreviewLine: (line: RenderLine | undefined) => void;
        setIsShowingPreview: (showing: boolean) => void;
    }
) {
    // Continue with selection logic
    const selected = selLogic.trySelect(event, ctrl);

    // Show preview based on current selection state
    if (selected && selected.length === 1) {
        // Line selected, show initial preview
        const selectedLine = selected[0] as RenderLine;
        toolState.setSelectedLine(selectedLine);
        const previewLine = showPreviewLineAfterLineSelection(selectedLine, ctrl, pQ, previewId, isPerpendicularVector);
        toolState.setPreviewLine(previewLine);
    } else if (selected && selected.length === 2) {
        // Line and point selected, show preview through point
        const selectedLine = selected[0] as RenderLine;
        const selectedPoint = selected[1] as RenderVertex;
        toolState.setSelectedLine(selectedLine);
        toolState.setSelectedPoint(selectedPoint);
        const previewLine = showPreviewLine(selectedLine, selectedPoint, ctrl, pQ, previewId, isPerpendicularVector);
        toolState.setPreviewLine(previewLine);
        toolState.setIsShowingPreview(true);
    } else if (selected && selected.length === 3) {
        // All selections complete, show final preview
        const selectedLine = selected[0] as RenderLine;
        const selectedPoint = selected[1] as RenderVertex;
        const finalVertex = vert(selected[2] as any);
        toolState.setSelectedLine(selectedLine);
        toolState.setSelectedPoint(selectedPoint);
        const previewLine = updatePreviewLineToVertex(
            selectedLine,
            finalVertex,
            ctrl,
            pQ,
            previewId,
            isPerpendicularVector
        );
        toolState.setPreviewLine(previewLine);
    }

    // Handle hover preview updates for final vertex selection
    if (selected && selected.length === 2 && toolState.isShowingPreview) {
        // Check if we're currently on the third selector (final vertex selector)
        if (selLogic.selectors && selLogic.selectors.length > 2) {
            const finalSelector = selLogic.selectors[2]; // The third selector is finalVertexSelector (OrSelector)

            // Try to get trial selection from the final vertex selector
            const trialVertex = finalSelector?.tryPreview?.(event, ctrl);

            if (trialVertex && toolState.selectedLine) {
                // Update preview line to go through the hovered vertex
                const previewLine = updatePreviewLineToVertex(
                    toolState.selectedLine,
                    trialVertex,
                    ctrl,
                    pQ,
                    previewId,
                    isPerpendicularVector
                );
                toolState.setPreviewLine(previewLine);
            }
        }
    }

    // Always flush at the end - this is the pattern used by other tools
    pQ.flush(ctrl);
}

/**
 * Handles vertex construction logic for final vertex selection
 * @param geoTool - The geometry tool instance
 * @param ctrl - Document controller
 * @param finalVertex - The final vertex
 * @param selectedPoint - The selected point
 * @returns Array of vertex constructions and the final vertex with name
 */
export async function handleVertexConstruction(
    geoTool: GeometryTool<CommonToolState>,
    ctrl: GeoDocCtrl,
    finalVertex: RenderVertex,
    _selectedPoint: RenderVertex
): Promise<{ vertexConstructions: GeoElConstructionRequest[]; namedFinalVertex: RenderVertex }> {
    // Check if final vertex needs construction (doesn't have a name and has negative relIndex)
    const needsVertexConstruction = !finalVertex.name && finalVertex.relIndex < 0;
    let vertexConstructions: GeoElConstructionRequest[] = [];

    if (needsVertexConstruction) {
        // Request name for the vertex
        const nt = geoTool.toolbar.getTool('NamingElementTool') as NamingElementTool;
        const inputPointNames = (
            await requestElementNames(ctrl, nt, [
                {
                    objName: 'Điểm',
                    originElement: [finalVertex],
                    pickName: pickPointName,
                    namesToAvoid: [],
                },
            ])
        )[0];

        if (!inputPointNames.length) {
            geoTool.resetState();
            throw new Error('No point names provided');
        }

        // Assign name to the vertex
        finalVertex.name = inputPointNames[0];

        // Create vertex construction
        const vertexConstruction = buildPointConstruction(finalVertex.name, {
            x: finalVertex.coords[0],
            y: finalVertex.coords[1],
        });
        vertexConstructions.push(vertexConstruction);
    }

    return { vertexConstructions, namedFinalVertex: finalVertex };
}

/**
 * Performs construction based on selected line, point, and final vertex selection
 * This function works with the selector-based workflow and handles both regular vertices
 * and vertices on strokes (VertexOnStroke)
 */
export async function performConstructionWithVertex(
    geoTool: GeometryTool<CommonToolState>,
    ctrl: GeoDocCtrl,
    object: {
        point: RenderVertex;
        line: RenderLine;
        finalVertex: RenderVertex;
        finalVertexSelection?: any; // The original selection result from selector (could be VertexOnStroke)
        buildLineSegmentWithIntersectLine: (
            name: string,
            lineStartName: string,
            lineStartType: string,
            intersectionLineName: string,
            intersectionLineType: string,
            throughPointName: string
        ) => GeoElConstructionRequest;
        buildLineSegment: (
            name: string,
            lineStartName: string,
            lineStartType: string,
            throughPointName: string,
            k: number
        ) => GeoElConstructionRequest;
        buildLine: (
            name: string,
            lineStartName: string,
            lineStartType: string,
            throughPointName: string
        ) => GeoElConstructionRequest;
        vertexConstructions?: GeoElConstructionRequest[]; // Optional vertex constructions
    },
    isPerpendicularVector: boolean = false
) {
    const finalVertexCoords = object.finalVertex.coords;

    let constructionAngle = undefined;

    const startPointName = object.point.name;
    const startLineName = object.line.name;
    const startLineType = object.line.elType;

    const vS = object.line.vector;
    const v = isPerpendicularVector ? [-vS[1], vS[0]] : vS;
    const startPoint = object.point.coords;
    const projectPoint = projectPointOntoLine(finalVertexCoords, startPoint, v);

    const vertex2: RenderVertex = {
        relIndex: -12,
        type: 'RenderVertex',
        elType: 'Point',
        renderProp: buildPreviewVertexRenderProp(),
        name: undefined,
        coords: projectPoint,
        usable: true,
        valid: true,
    };

    const isPointerAtStartPosition = isSamePoint(finalVertexCoords, object.point.coords, ctrl);
    const isPointerOnPerpendicularLine = isSamePoint(finalVertexCoords, projectPoint, ctrl);
    const nt = geoTool.toolbar.getTool('NamingElementTool') as NamingElementTool;

    // Check if the final vertex is on a stroke (VertexOnStroke selection)
    let intersectLine: RenderLine | undefined;
    if (
        object.finalVertexSelection &&
        Array.isArray(object.finalVertexSelection) &&
        object.finalVertexSelection.length === 2
    ) {
        // This is a VertexOnStroke selection: [StrokeType, RenderVertex]
        const [stroke, _vertex] = object.finalVertexSelection;
        // Check if the stroke is not the preview line (preview lines have negative relIndex)
        if (stroke.relIndex >= 0 && isElementLine(stroke)) {
            intersectLine = stroke as RenderLine;
        }
    }

    // Choose construction type based on final vertex position and whether it's on an existing line
    if (intersectLine && !isPointerAtStartPosition) {
        // Create line segment with intersection line
        const inputPointNames = (
            await requestElementNames(ctrl, nt, [
                {
                    objName: 'Đoạn Thẳng',
                    originElement: [object.point, vertex2],
                    pickName: pickPointName,
                    namesToAvoid: [],
                },
            ])
        )[0];
        if (!inputPointNames.length) {
            geoTool.resetState();
            return;
        }

        constructionAngle = object.buildLineSegmentWithIntersectLine(
            inputPointNames.join(''),
            startLineName,
            startLineType,
            intersectLine.name,
            intersectLine.elType,
            startPointName
        );
    } else if (!isPointerAtStartPosition && isPointerOnPerpendicularLine) {
        // Create line segment (vertex is on preview line only)
        const inputPointNames = (
            await requestElementNames(ctrl, nt, [
                {
                    objName: 'Đoạn Thẳng',
                    originElement: [object.point, vertex2],
                    pickName: pickPointName,
                    namesToAvoid: [],
                },
            ])
        )[0];
        if (!inputPointNames.length) {
            geoTool.resetState();
            return;
        }

        const uVector = calculateUnitVector(v);
        const k = calculateScalingFactor(uVector, startPoint, projectPoint);
        const lVector = [projectPoint[0] - startPoint[0], projectPoint[1] - startPoint[1]];

        const lineDirection =
            vector(v[0], v[1]).angleTo(vector(lVector[0], lVector[1])) * (180 / Math.PI) == 180 ? -1 : 1;

        constructionAngle = object.buildLineSegment(
            inputPointNames.join(''),
            startLineName,
            startLineType,
            startPointName,
            k * lineDirection
        );
    } else {
        // Create straight line
        constructionAngle = object.buildLine('', startLineName, startLineType, startPointName);
    }

    geoTool.resetState();

    await ctrl.editor.awarenessFeature.useAwareness(
        ctrl.viewport.id,
        'Đang tạo hình',
        buildDocumentAwarenessCmdOption(ctrl.editor.awarenessConstructId, ctrl),
        async () => {
            // Prepare construction requests - include vertex constructions if any
            const constructionRequests = [];

            // Add vertex constructions first (if any)
            if (object.vertexConstructions && object.vertexConstructions.length > 0) {
                object.vertexConstructions.forEach(vertexConstruction => {
                    constructionRequests.push({
                        construction: vertexConstruction,
                    });
                });
            }

            // Add the main construction
            constructionRequests.push({
                construction: constructionAngle,
            });

            const constructResponse = await geoTool.editor.geoGateway.construct(
                ctrl.state.globalId,
                constructionRequests
            );

            await syncRenderCommands(constructResponse.render, ctrl);
            addHistoryItemFromConstructionResponse(ctrl, constructResponse);
            geoTool.resetState();
        }
    );
}

export async function onFinalClick(
    geoTool: GeometryTool<CommonToolState>,
    event: GeoPointerEvent,
    object: {
        point: RenderVertex;
        line: RenderLine;
        lastHitCtx: GeoSelectHitContext | null;
        buildLineSegmentWithIntersectLine: (
            name: string,
            lineStartName: string,
            lineStartType: string,
            intersectionLineName: string,
            intersectionLineType: string,
            throughPointName: string
        ) => GeoElConstructionRequest;
        buildLineSegment: (
            name: string,
            lineStartName: string,
            lineStartType: string,
            throughPointName: string,
            k: number
        ) => GeoElConstructionRequest;
        buildLine: (
            name: string,
            lineStartName: string,
            lineStartType: string,
            throughPointName: string
        ) => GeoElConstructionRequest;
    },
    isPerpendicularVector: boolean = false
) {
    const { ctrl, pos, hitCtx, hitEl } = geoTool.posAndCtrl(event);
    let mpg = [pos.x, pos.y];
    let intersectLine: RenderLine;

    if (hitEl) {
        ctrl.editor.selectElement(hitCtx, true);

        if (hitEl.type == 'RenderVertex') {
            const e = hitEl as RenderVertex;
            mpg = e.coords;
        }

        if (isElementLine(hitEl)) {
            intersectLine = hitEl as RenderLine;
        }
    }

    let constructionAngle = undefined;

    const startPointName = object.point.name;
    const startLineName = object.line.name;
    const startLineType = object.line.elType;

    const vS = object.line.vector;
    const v = isPerpendicularVector ? [-vS[1], vS[0]] : vS;
    const startPoint = object.point.coords;
    const projectPoint = projectPointOntoLine(mpg, startPoint, v);

    let projectPointIntersect: number[];

    if (intersectLine) {
        const vI = intersectLine.vector;
        const v1 = [-vI[1], vI[0]];
        projectPointIntersect = projectPointOntoLine(mpg, startPoint, v1);
    }

    const vertex2: RenderVertex = {
        relIndex: -12,
        type: 'RenderVertex',
        elType: 'Point',
        renderProp: buildPreviewVertexRenderProp(),
        name: undefined,
        coords: projectPoint,
        usable: true,
        valid: true,
    };

    const haveIntersectLine = !!intersectLine;
    const isPointStartOnIntersectLine = isSamePoint(object.point.coords, projectPointIntersect, ctrl);
    const isPointerAtStartPosition = isSamePoint(mpg, object.point.coords, ctrl);
    const isPointerOnPerpendicularLine = isSamePoint(mpg, projectPoint, ctrl);
    const nt = geoTool.toolbar.getTool('NamingElementTool') as NamingElementTool;
    // Selected 2nd line and start point is not on 2nd line (create line segment)
    if (haveIntersectLine && !isPointStartOnIntersectLine) {
        const inputPointNames = (
            await requestElementNames(ctrl, nt, [
                {
                    objName: 'Đoạn Thẳng',
                    originElement: [object.point, vertex2],
                    pickName: pickPointName,
                    namesToAvoid: [],
                },
            ])
        )[0];
        if (!inputPointNames.length) {
            geoTool.resetState();
            return;
        }

        const intersectionLineName = intersectLine.name;
        constructionAngle = object.buildLineSegmentWithIntersectLine(
            inputPointNames.join(''),
            startLineName,
            startLineType,
            intersectionLineName,
            intersectLine.elType,
            startPointName
        );
        // choose a point on the perpendicular line and the chosen point is not the starting point (create line segment)
    } else if (!isPointerAtStartPosition && isPointerOnPerpendicularLine) {
        const inputPointNames = (
            await requestElementNames(ctrl, nt, [
                {
                    objName: 'Đoạn Thẳng',
                    originElement: [object.point, vertex2],
                    pickName: pickPointName,
                    namesToAvoid: [],
                },
            ])
        )[0];
        if (!inputPointNames.length) {
            geoTool.resetState();
            return;
        }

        const uVector = calculateUnitVector(v);
        const k = calculateScalingFactor(uVector, startPoint, projectPoint);
        const lVector = [projectPoint[0] - startPoint[0], projectPoint[1] - startPoint[1]];

        const lineDirection =
            vector(v[0], v[1]).angleTo(vector(lVector[0], lVector[1])) * (180 / Math.PI) == 180 ? -1 : 1;

        constructionAngle = object.buildLineSegment(
            inputPointNames.join(''),
            startLineName,
            startLineType,
            startPointName,
            k * lineDirection
        );
        // create straight line
    } else constructionAngle = object.buildLine('', startLineName, startLineType, startPointName);

    geoTool.resetState();

    await ctrl.editor.awarenessFeature.useAwareness(
        ctrl.viewport.id,
        'Đang tạo hình',
        buildDocumentAwarenessCmdOption(ctrl.editor.awarenessConstructId, ctrl),
        async () => {
            const constructResponse = await geoTool.editor.geoGateway.construct(ctrl.state.globalId, [
                {
                    construction: constructionAngle,
                },
            ]);

            await syncRenderCommands(constructResponse.render, ctrl);
            addHistoryItemFromConstructionResponse(ctrl, constructResponse);
            geoTool.resetState();
        }
    );
}
